
import React from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import WhatsAppButton from "@/components/home/<USER>";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ArrowRight,
  Phone,
  MessageCircle,
  Calendar,
  Car,
  ScanLine,
  Battery,
  Wrench,
  CheckCircle,
  Clock,
  MapPin,
  Shield,
  Star,
  Users,
  AlertTriangle,
  HelpCircle,
  ChevronRight
} from "lucide-react";

const Index = () => {
  // Core services data
  const coreServices = [
    {
      icon: Car,
      title: "Engine Diagnostics",
      description: "Complete engine system analysis and fault detection",
      price: "From KES 2,000",
      features: ["Check Engine Light", "Performance Issues", "Fuel System"]
    },
    {
      icon: ScanLine,
      title: "Full System Scan",
      description: "Comprehensive vehicle health check of all systems",
      price: "From KES 3,000",
      features: ["All ECU Modules", "Complete Report", "Live Data"]
    },
    {
      icon: Battery,
      title: "Electrical Diagnostics",
      description: "Battery, alternator, and electrical system testing",
      price: "From KES 1,500",
      features: ["Battery Health", "Charging System", "Electrical Faults"]
    },
    {
      icon: Wrench,
      title: "Emergency Service",
      description: "24/7 urgent diagnostic service for breakdowns",
      price: "From KES 4,000",
      features: ["Priority Response", "Critical Issues", "Immediate Help"]
    }
  ];

  // Trust indicators
  const trustStats = [
    { number: "500+", label: "Happy Customers", icon: Users },
    { number: "4.9/5", label: "Customer Rating", icon: Star },
    { number: "< 30min", label: "Response Time", icon: Clock },
    { number: "100%", label: "Satisfaction Guarantee", icon: Shield }
  ];

  // Key FAQs
  const keyFAQs = [
    {
      question: "How quickly can you reach my location?",
      answer: "Our average response time is 15-45 minutes within Nairobi, with emergency services guaranteed within 60 minutes."
    },
    {
      question: "What areas do you cover?",
      answer: "We serve greater Nairobi including Westlands, Karen, CBD, Upperhill, Langata, and surrounding areas."
    },
    {
      question: "How much do diagnostic services cost?",
      answer: "Services start from KES 1,500 for basic diagnostics up to KES 4,000 for emergency services. All pricing is transparent with no hidden fees."
    }
  ];

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerChildren = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main>
        {/* Hero Section - Minimal & Focused */}
        <section className="relative bg-gradient-to-r from-automotive-dark to-automotive-blue text-white py-20 lg:py-32">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="max-w-4xl mx-auto text-center"
              initial="initial"
              animate="animate"
              variants={fadeInUp}
            >
              <Badge variant="outline" className="border-automotive-orange text-automotive-orange mb-6 text-sm">
                Mobile Automotive Diagnostics
              </Badge>
              <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                Professional Vehicle
                <span className="text-automotive-orange"> Diagnostics</span>
                <br />at Your Location
              </h1>
              <p className="text-xl lg:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
                Expert mobile diagnostic services in Nairobi. We come to you with professional equipment
                and certified technicians.
              </p>

              {/* Primary CTAs */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button
                  size="lg"
                  className="bg-automotive-orange hover:bg-automotive-orange/90 text-white px-8 py-4 text-lg"
                  asChild
                >
                  <Link to="/booking">
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Service Now
                  </Link>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-automotive-dark px-8 py-4 text-lg"
                  asChild
                >
                  <a href="tel:+254727795520">
                    <Phone className="w-5 h-5 mr-2" />
                    Call: 0727 795 520
                  </a>
                </Button>
              </div>

              {/* Trust Indicators */}
              <motion.div
                className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto"
                variants={staggerChildren}
              >
                {trustStats.map((stat, index) => (
                  <motion.div key={index} variants={fadeInUp} className="text-center">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                      <stat.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-2xl font-bold text-automotive-orange">{stat.number}</div>
                    <div className="text-sm text-white/80">{stat.label}</div>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Core Services - Streamlined */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-automotive-dark mb-4">
                Our Core Services
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Professional mobile diagnostic services for all your vehicle needs
              </p>
            </motion.div>

            <motion.div
              className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              {coreServices.map((service, index) => (
                <motion.div key={index} variants={fadeInUp}>
                  <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                    <CardHeader className="text-center pb-4">
                      <div className="w-16 h-16 bg-automotive-blue/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <service.icon className="w-8 h-8 text-automotive-blue" />
                      </div>
                      <CardTitle className="text-lg font-bold text-automotive-dark">
                        {service.title}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">{service.description}</p>
                    </CardHeader>
                    <CardContent className="text-center">
                      <div className="text-xl font-bold text-automotive-orange mb-4">{service.price}</div>
                      <div className="space-y-2">
                        {service.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center text-sm text-muted-foreground">
                            <CheckCircle className="w-4 h-4 text-automotive-blue mr-2 flex-shrink-0" />
                            {feature}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>

            <div className="text-center">
              <Button
                variant="outline"
                size="lg"
                className="border-automotive-blue text-automotive-blue hover:bg-automotive-blue hover:text-white"
                asChild
              >
                <Link to="/services">
                  View All Services
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* How It Works - Simplified */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-automotive-dark mb-4">
                How It Works
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Simple, fast, and professional mobile diagnostic service
              </p>
            </motion.div>

            <motion.div
              className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              {[
                {
                  step: "1",
                  title: "Book Online or Call",
                  description: "Schedule your service through our website or call us directly",
                  icon: Calendar
                },
                {
                  step: "2",
                  title: "We Come to You",
                  description: "Our certified technician arrives at your location with professional equipment",
                  icon: Car
                },
                {
                  step: "3",
                  title: "Get Results",
                  description: "Receive detailed diagnostic report and repair recommendations",
                  icon: CheckCircle
                }
              ].map((step, index) => (
                <motion.div key={index} variants={fadeInUp} className="text-center">
                  <div className="relative mb-6">
                    <div className="w-20 h-20 bg-automotive-blue rounded-full flex items-center justify-center mx-auto">
                      <step.icon className="w-10 h-10 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-automotive-orange rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{step.step}</span>
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-automotive-dark mb-3">{step.title}</h3>
                  <p className="text-muted-foreground">{step.description}</p>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Emergency Services - Prominent */}
        <section className="py-16 bg-gradient-to-r from-red-50 to-orange-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="max-w-4xl mx-auto"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <Card className="bg-gradient-to-r from-red-600 to-automotive-orange text-white border-0 shadow-2xl">
                <CardContent className="p-8 lg:p-12">
                  <div className="grid lg:grid-cols-2 gap-8 items-center">
                    <div>
                      <div className="flex items-center gap-2 mb-4">
                        <AlertTriangle className="w-6 h-6 text-yellow-300" />
                        <Badge variant="outline" className="border-yellow-300 text-yellow-300">
                          24/7 Emergency
                        </Badge>
                      </div>
                      <h2 className="text-3xl lg:text-4xl font-bold mb-4">
                        Vehicle Breakdown?
                      </h2>
                      <p className="text-xl text-white/90 mb-6">
                        Don't panic! Our emergency response team is available 24/7 with guaranteed
                        arrival within 60 minutes in Nairobi.
                      </p>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 text-white/90">
                          <CheckCircle className="w-5 h-5 text-yellow-300" />
                          <span>24/7 availability</span>
                        </div>
                        <div className="flex items-center gap-2 text-white/90">
                          <CheckCircle className="w-5 h-5 text-yellow-300" />
                          <span>Priority response under 60 minutes</span>
                        </div>
                        <div className="flex items-center gap-2 text-white/90">
                          <CheckCircle className="w-5 h-5 text-yellow-300" />
                          <span>Professional safety protocols</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-5xl font-bold text-yellow-300 mb-2">0727 795 520</div>
                      <p className="text-white/90 mb-6">Emergency Hotline</p>
                      <Button
                        size="lg"
                        className="bg-white text-red-600 hover:bg-white/90 font-bold px-8 py-4"
                        asChild
                      >
                        <a href="tel:+254727795520">
                          <Phone className="w-5 h-5 mr-2" />
                          CALL NOW - EMERGENCY
                        </a>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </section>

        {/* Key FAQs - Essential Questions Only */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-automotive-dark mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Quick answers to common questions about our mobile diagnostic services
              </p>
            </motion.div>

            <motion.div
              className="max-w-3xl mx-auto space-y-6 mb-12"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              {keyFAQs.map((faq, index) => (
                <motion.div key={index} variants={fadeInUp}>
                  <Card className="border border-gray-200 hover:border-automotive-blue/50 transition-colors">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-automotive-dark mb-3 flex items-start gap-2">
                        <HelpCircle className="w-5 h-5 text-automotive-blue mt-0.5 flex-shrink-0" />
                        {faq.question}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed pl-7">
                        {faq.answer}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>

            <div className="text-center">
              <Button
                variant="outline"
                size="lg"
                className="border-automotive-blue text-automotive-blue hover:bg-automotive-blue hover:text-white"
                asChild
              >
                <Link to="/faq">
                  View All FAQs
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Contact & Service Area - Essential Info */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-automotive-dark mb-4">
                Ready to Get Started?
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Professional mobile automotive diagnostics across Nairobi and surrounding areas
              </p>
            </motion.div>

            <motion.div
              className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              {/* Contact Methods */}
              <motion.div variants={fadeInUp}>
                <h3 className="text-2xl font-bold text-automotive-dark mb-6">Get In Touch</h3>
                <div className="space-y-4">
                  {[
                    {
                      icon: Phone,
                      title: "Call Us",
                      value: "0727 795 520",
                      description: "24/7 Emergency | 8 AM - 6 PM Regular",
                      href: "tel:+254727795520",
                      color: "bg-automotive-blue"
                    },
                    {
                      icon: MessageCircle,
                      title: "WhatsApp",
                      value: "Quick Support",
                      description: "Fast responses and booking assistance",
                      href: "https://wa.me/254727795520",
                      color: "bg-green-600"
                    },
                    {
                      icon: Calendar,
                      title: "Online Booking",
                      value: "Schedule Service",
                      description: "Book your appointment online 24/7",
                      href: "/booking",
                      color: "bg-automotive-orange"
                    }
                  ].map((contact, index) => (
                    <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-shadow">
                      <CardContent className="p-4">
                        {contact.href.startsWith('http') || contact.href.startsWith('tel:') ? (
                          <a href={contact.href} className="flex items-center gap-4 group">
                            <div className={`w-12 h-12 ${contact.color} rounded-full flex items-center justify-center`}>
                              <contact.icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <h4 className="font-semibold text-automotive-dark group-hover:text-automotive-blue transition-colors">
                                {contact.title}
                              </h4>
                              <p className="text-sm font-medium text-automotive-blue">{contact.value}</p>
                              <p className="text-xs text-muted-foreground">{contact.description}</p>
                            </div>
                            <ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-automotive-blue transition-colors" />
                          </a>
                        ) : (
                          <Link to={contact.href} className="flex items-center gap-4 group">
                            <div className={`w-12 h-12 ${contact.color} rounded-full flex items-center justify-center`}>
                              <contact.icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <h4 className="font-semibold text-automotive-dark group-hover:text-automotive-blue transition-colors">
                                {contact.title}
                              </h4>
                              <p className="text-sm font-medium text-automotive-blue">{contact.value}</p>
                              <p className="text-xs text-muted-foreground">{contact.description}</p>
                            </div>
                            <ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-automotive-blue transition-colors" />
                          </Link>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </motion.div>

              {/* Service Area & Business Info */}
              <motion.div variants={fadeInUp}>
                <h3 className="text-2xl font-bold text-automotive-dark mb-6">Service Information</h3>
                <div className="space-y-6">
                  <Card className="border-0 shadow-md">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-3 mb-4">
                        <MapPin className="w-6 h-6 text-automotive-blue mt-1" />
                        <div>
                          <h4 className="font-semibold text-automotive-dark">Service Coverage</h4>
                          <p className="text-sm text-muted-foreground">Greater Nairobi Area</p>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <p>• Westlands, Karen, CBD, Upperhill</p>
                        <p>• Langata, Kasarani, Embakasi</p>
                        <p>• Kiambu, Thika Road corridor</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-md">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-3 mb-4">
                        <Clock className="w-6 h-6 text-automotive-orange mt-1" />
                        <div>
                          <h4 className="font-semibold text-automotive-dark">Business Hours</h4>
                          <p className="text-sm text-muted-foreground">Regular Service</p>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <p>Monday - Saturday: 8:00 AM - 6:00 PM</p>
                        <p>Emergency Services: 24/7</p>
                        <p>Response Time: 15-45 minutes</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-md">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-3 mb-4">
                        <Shield className="w-6 h-6 text-green-600 mt-1" />
                        <div>
                          <h4 className="font-semibold text-automotive-dark">Our Guarantee</h4>
                          <p className="text-sm text-muted-foreground">Quality Assurance</p>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <p>• 100% satisfaction guarantee</p>
                        <p>• Licensed & insured technicians</p>
                        <p>• Transparent pricing</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Final CTA - Clean & Direct */}
        <section className="py-20 bg-gradient-to-r from-automotive-dark to-automotive-blue text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center max-w-3xl mx-auto"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-3xl lg:text-4xl font-bold mb-6">
                Professional Mobile Diagnostics
              </h2>
              <p className="text-xl text-white/90 mb-8">
                Don't let vehicle problems stress you out. Get professional diagnostic services
                at your location in Nairobi.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-automotive-orange hover:bg-automotive-orange/90 text-white px-8 py-4 text-lg"
                  asChild
                >
                  <Link to="/booking">
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Service Now
                  </Link>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white/10 px-8 py-4 text-lg"
                  asChild
                >
                  <Link to="/contact">
                    <MessageCircle className="w-5 h-5 mr-2" />
                    Get More Info
                  </Link>
                </Button>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
      <WhatsAppButton />
    </div>
  );
};

export default Index;


